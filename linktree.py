import sys
import async<PERSON>
import json
import os
from core.constants import FIREBASE_URL
from data_handler import process_and_upload_json
from linktree_scraper import LinktreeScraper
from services.upload_avatar import upload_profile_image


async def main():
    if len(sys.argv) < 2:
        print("Username or url is needed!")
        sys.exit(1)

    input = sys.argv[1]
    if "linktr.ee" in input:
        username, url = None, input
    else:
        username, url = input, None

    try:
        # Initialize components
        scraper = LinktreeScraper()

        # Create output directory if it doesn't exist
        output_dir = os.path.join(os.getcwd(), "output")
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Get user info
        print(f"\n[INFO] Fetching data for {username or url}...")
        try:
            user_info = await scraper.getLinktreeUserInfo(username=username, url=url)
        except FileNotFoundError as e:
            print(f"\n[ERROR] User data file not found: {e}")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"\n[ERROR] Failed to decode user data JSON: {e}")
            sys.exit(1)

        # Create user-specific directory
        user_dir = os.path.join(output_dir, user_info.username)
        if not os.path.exists(user_dir):
            os.makedirs(user_dir)

        # Print basic info
        print("\n[SUCCESS] Found user information:")
        print(f"Username: {user_info.username}")
        print(f"Description: {user_info.description}")
        print(f"Number of links: {len(user_info.links)}")

        # Upload profile image to ImgBB if exists
        await upload_profile_image(user_info)

        # Export and generate unified JSON
        print("\n[INFO] Generating unified JSON...")
        await scraper.generate_unified_json(user_info, user_info.username)

        # Read the unified JSON - note the correct filename here
        from services.utils.normalize_username import normalize_username

        normalized_username = normalize_username(user_info.username)
        normalized_user_dir = os.path.join(output_dir, normalized_username)
        unified_json_path = os.path.join(
            normalized_user_dir, f"{normalized_username}.json"
        )

        # Wait a bit to ensure file is written
        max_retries = 5
        for i in range(max_retries):
            if os.path.exists(unified_json_path):
                break
            print(
                f"[INFO] Waiting for JSON to be created (attempt {i+1}/{max_retries})..."
            )
            await asyncio.sleep(1)

        if not os.path.exists(unified_json_path):
            raise FileNotFoundError(f"JSON file was not created in {unified_json_path}")

        with open(unified_json_path, "r", encoding="utf-8") as f:
            unified_data = json.load(f)

        # Process with AI and upload to Firebase
        print("\n[INFO] Optimizing data with AI for link page format...")

        # Create the expected structure for Firebase
        firebase_input = {
            user_info.username: unified_data,
        }

        try:
            await process_and_upload_json(firebase_input, use_ai=True)
            print("\n[SUCCESS] Data processed and uploaded to Firebase")
            print(
                f"[INFO] View your data at: {FIREBASE_URL}/users/{normalize_username(user_info.username)}.json"
            )
        except Exception as e:
            print(f"\n[ERROR] Error uploading to Firebase: {str(e)}")
            sys.exit(1)

    except Exception as e:
        print(f"\n[ERROR] An unexpected error occurred: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
