"""
User input parser utility for LinkTree scraper.

This module provides functionality to parse flexible user input formats
and extract clean username and URL for LinkTree profiles.
"""

import re
from typing import Tuple
from urllib.parse import urlparse


def parse_user_input(user_input: str) -> Tuple[str, str]:
    """
    Parse flexible user input and extract username and URL.
    
    Supports the following input formats:
    - Full URLs: "https://linktr.ee/barbeariatarantino"
    - URLs without protocol: "linktr.ee/barbeariatarantino"
    - Just username: "barbeariatarantino"
    
    Args:
        user_input (str): The user input to parse
        
    Returns:
        Tuple[str, str]: (username, url) where url is the full LinkTree URL
        
    Raises:
        ValueError: If the input is invalid or cannot be parsed
    """
    if not user_input or not user_input.strip():
        raise ValueError("User input cannot be empty")
    
    # Clean the input
    user_input = user_input.strip()
    
    # Check if it's a URL (contains linktr.ee)
    if "linktr.ee" in user_input.lower():
        return _parse_url_input(user_input)
    else:
        return _parse_username_input(user_input)


def _parse_url_input(url_input: str) -> Tuple[str, str]:
    """
    Parse URL input and extract username.
    
    Args:
        url_input (str): URL input containing linktr.ee
        
    Returns:
        Tuple[str, str]: (username, normalized_url)
        
    Raises:
        ValueError: If URL is invalid or doesn't contain a valid username
    """
    # Normalize URL - add https:// if missing
    if not url_input.startswith(("http://", "https://")):
        url_input = "https://" + url_input
    
    # Parse the URL
    try:
        parsed = urlparse(url_input)
    except Exception as e:
        raise ValueError(f"Invalid URL format: {e}")
    
    # Validate domain
    if "linktr.ee" not in parsed.netloc.lower():
        raise ValueError("URL must be a valid Linktree URL (linktr.ee)")
    
    # Extract username from path
    path = parsed.path.strip("/")
    if not path:
        raise ValueError("URL must contain a username path")
    
    # Get the first path segment as username (ignore additional paths)
    username = path.split("/")[0]
    
    # Remove query parameters from username if any
    username = username.split("?")[0]
    
    if not username:
        raise ValueError("Could not extract username from URL")
    
    # Validate and clean username
    username = _validate_and_clean_username(username)
    
    # Construct normalized URL
    normalized_url = f"https://linktr.ee/{username}"
    
    return username, normalized_url


def _parse_username_input(username_input: str) -> Tuple[str, str]:
    """
    Parse username input and construct URL.
    
    Args:
        username_input (str): Username input
        
    Returns:
        Tuple[str, str]: (cleaned_username, constructed_url)
        
    Raises:
        ValueError: If username is invalid
    """
    # Validate and clean username
    username = _validate_and_clean_username(username_input)
    
    # Construct URL
    url = f"https://linktr.ee/{username}"
    
    return username, url


def _validate_and_clean_username(username: str) -> str:
    """
    Validate and clean a username.
    
    Args:
        username (str): Raw username to validate
        
    Returns:
        str: Cleaned and validated username
        
    Raises:
        ValueError: If username is invalid
    """
    if not username or not username.strip():
        raise ValueError("Username cannot be empty")
    
    # Remove leading/trailing whitespace
    username = username.strip()
    
    # Remove @ symbol if present (common in social media contexts)
    username = username.lstrip("@")
    
    # Check length constraints
    if len(username) < 1:
        raise ValueError("Username too short")
    if len(username) > 100:
        raise ValueError("Username too long (max 100 characters)")
    
    # Check for valid characters (alphanumeric, dots, underscores, hyphens)
    if not re.match(r"^[a-zA-Z0-9._-]+$", username):
        raise ValueError(
            "Username contains invalid characters. Only letters, numbers, dots, underscores, and hyphens are allowed"
        )
    
    # Check that it doesn't start or end with special characters
    if username.startswith((".", "_", "-")) or username.endswith((".", "_", "-")):
        raise ValueError("Username cannot start or end with special characters")
    
    # Check for consecutive special characters
    if re.search(r"[._-]{2,}", username):
        raise ValueError("Username cannot contain consecutive special characters")
    
    return username


def validate_info_parameter(info: str) -> str:
    """
    Validate and clean the info parameter.
    
    Args:
        info (str): Additional information parameter
        
    Returns:
        str: Cleaned info parameter
        
    Raises:
        ValueError: If info parameter is invalid
    """
    if not info:
        return ""
    
    # Remove leading/trailing whitespace
    info = info.strip()
    
    # Check length constraints
    if len(info) > 1000:
        raise ValueError("Info parameter too long (max 1000 characters)")
    
    # Basic sanitization - remove control characters but keep newlines and tabs
    info = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", info)
    
    return info
