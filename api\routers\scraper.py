from fastapi import APIRouter, HTTPException
from api.schemas.requests import ScrapeUserRequest
from api.schemas.responses import (
    ScrapeUserResponse,
    ProcessCompleteResponse,
)
from api.services.scraper_service import scraper_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Scraper"], prefix="/scrape")


@router.post("/user", response_model=ScrapeUserResponse)
async def scrape_user(request: ScrapeUserRequest):
    """
    Endpoint para realizar scraping de um usuário do Linktree

    Este endpoint aceita os seguintes parâmetros:
    - user: Identificador do usuário (username, URL completa, ou linktr.ee URL)
    - info: Informações adicionais para processamento (opcional)

    O parâmetro 'user' aceita formatos flexíveis:
    - Username: "barbeariatarantino"
    - URL completa: "https://linktr.ee/barbeariatarantino"
    - URL sem protocolo: "linktr.ee/barbeariatarantino"

    Args:
        request: Dados da requisição contendo user (obrigatório) e info (opcional)

    Returns:
        ScrapeUserResponse: Dados do usuário ou erro

    Raises:
        HTTPException: Em caso de erro de validação ou processamento
    """
    try:
        # Parse user input to get username and URL
        username, url = request.get_parsed_user_data()
        logger.info(f"Received scrape request for user: {username}")

        # Chamar o serviço de scraping
        result = await scraper_service.scrape_user_by_username(
            username=username, url=url, info=request.info
        )

        # Retornar resposta baseada no resultado
        response = ScrapeUserResponse(
            success=result["success"], message=result["message"], data=result["data"]
        )

        if not result["success"]:
            logger.warning(
                f"Scraping failed for user {request.username}: {result['message']}"
            )

        return response

    except ValueError as e:
        logger.error(f"Validation error for user {request.username}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Erro de validação: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error during scraping for user {request.username}: {str(e)}"
        )
        raise HTTPException(
            status_code=500, detail="Erro interno do servidor durante o scraping"
        )


@router.post("/process", response_model=ProcessCompleteResponse)
async def process_user_complete(request: ScrapeUserRequest):
    """
    Endpoint para realizar o processo completo de scraping e upload para Firebase

    Este endpoint executa todo o processo que é equivalente ao comando:
    python linktree.py USERNAME

    Ou seja, realiza:
    1. Extração dos dados do usuário
    2. Upload da imagem de perfil
    3. Geração do JSON unificado
    4. Processamento com IA
    5. Upload para Firebase

    Args:
        request: Dados da requisição contendo username OU url (pelo menos um obrigatório)

    Returns:
        ProcessCompleteResponse: Resultado do processo completo

    Raises:
        HTTPException: Em caso de erro de validação ou processamento
    """
    try:
        # Parse user input to get username and URL
        username, url = request.get_parsed_user_data()
        logger.info(f"Received complete process request for user: {username}")

        # Chamar o serviço de processamento completo
        result = await scraper_service.process_user_complete(
            username=username, url=url, info=request.info
        )

        # Retornar resposta baseada no resultado
        response = ProcessCompleteResponse(
            success=result["success"],
            message=result["message"],
            data=result["data"],
            firebase_url=result.get("firebase_url"),
        )

        if not result["success"]:
            logger.warning(
                f"Complete process failed for user {username}: {result['message']}"
            )

        return response

    except ValueError as e:
        logger.error(f"Validation error for user {request.username}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Erro de validação: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error during complete process for user {request.username}: {str(e)}"
        )
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor durante o processamento completo",
        )
